using Moq;
using Shouldly;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Logging;

namespace Zify.Settlement.Application.UnitTests.Infrastructure.Logging;

public class SinkSelectionFactoryTests
{
    private readonly Mock<TcpSinkStrategy> _tcpStrategyMock;
    private readonly Mock<UdpSinkStrategy> _udpStrategyMock;
    private readonly SinkSelectionFactory _factory;

    public SinkSelectionFactoryTests()
    {
        _tcpStrategyMock = new Mock<TcpSinkStrategy>();
        _udpStrategyMock = new Mock<UdpSinkStrategy>();

        var strategies = new List<ISinkSelectionStrategy>
        {
            _tcpStrategyMock.Object,
            _udpStrategyMock.Object
        };

        _factory = new SinkSelectionFactory(strategies);
    }

    [Fact]
    public void Constructor_WithNullStrategies_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        Should.Throw<ArgumentNullException>(() => new SinkSelectionFactory(null!));
    }

    [Fact]
    public void GetStrategy_WithTcpType_ShouldReturnTcpStrategy()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = LogstashTransportType.Tcp };

        // Act
        var result = _factory.GetStrategy(options);

        // Assert
        result.ShouldBe(_tcpStrategyMock.Object);
    }

    [Fact]
    public void GetStrategy_WithUdpType_ShouldReturnUdpStrategy()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = LogstashTransportType.Udp };

        // Act
        var result = _factory.GetStrategy(options);

        // Assert
        result.ShouldBe(_udpStrategyMock.Object);
    }

    [Fact]
    public void GetStrategy_WithUnsupportedType_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = LogstashTransportType.Unknown };

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => _factory.GetStrategy(options));
        exception.Message.ShouldContain("No sink selection strategy found for Logstash type");
        exception.Message.ShouldContain("Supported types are: TCP, UDP");
    }

    [Fact]
    public void GetAllStrategies_ShouldReturnAllRegisteredStrategies()
    {
        // Act
        var result = _factory.GetAllStrategies().ToList();

        // Assert
        result.Count.ShouldBe(2);
        result.ShouldContain(_tcpStrategyMock.Object);
        result.ShouldContain(_udpStrategyMock.Object);
    }
}
