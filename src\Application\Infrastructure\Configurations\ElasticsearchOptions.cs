using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Serilog.Events;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

/// <summary>
/// Defines the supported Logstash transport types.
/// </summary>
public enum LogstashTransportType
{
    Unknown = 0,
    Tcp,
    Udp
}

/// <summary>
/// Configuration options for Elasticsearch and Logstash integration.
/// </summary>
public sealed class ElasticsearchOptions
{
    public const string SectionName = "Elasticsearch";

    /// <summary>
    /// Gets or sets the Elasticsearch server URI.
    /// </summary>
    [ConfigurationKeyName("Elasticsearch_Uri")]
    public string Uri { get; set; } = "http://localhost:9200";

    /// <summary>
    /// Gets or sets the index name pattern for Elasticsearch.
    /// </summary>
    [ConfigurationKeyName("Elasticsearch_IndexName")]
    public string IndexName { get; set; } = "zify-settlement-logs";

    /// <summary>
    /// Gets or sets the username for Elasticsearch authentication.
    /// </summary>
    [ConfigurationKeyName("Elasticsearch_Username")]
    public string? Username { get; set; }

    /// <summary>
    /// Gets or sets the password for Elasticsearch authentication.
    /// </summary>
    [ConfigurationKeyName("Elasticsearch_Password")]
    public string? Password { get; set; }

    /// <summary>
    /// Gets or sets the Logstash server address.
    /// </summary>
    [ConfigurationKeyName("Logstash_Address")]
    public string LogstashAddress { get; set; } = "localhost";

    /// <summary>
    /// Gets or sets the Logstash server port.
    /// </summary>
    [ConfigurationKeyName("Logstash_Port")]
    public int LogstashPort { get; set; } = 9513;

    /// <summary>
    /// Gets or sets the Logstash connection type (TCP or UDP).
    /// This determines which sink strategy to use for log forwarding.
    /// </summary>
    [ConfigurationKeyName("Logstash_Type")]
    public LogstashTransportType LogstashType { get; set; } = LogstashTransportType.Tcp;

    /// <summary>
    /// Gets or sets whether to enable Elasticsearch sink.
    /// </summary>
    [ConfigurationKeyName("Elasticsearch_Enabled")]
    public bool ElasticsearchEnabled { get; set; } = false;

    /// <summary>
    /// Gets or sets whether to enable Logstash sink.
    /// </summary>
    [ConfigurationKeyName("Logstash_Enabled")]
    public bool LogstashEnabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the connection timeout in milliseconds.
    /// </summary>
    public int ConnectionTimeout { get; set; } = 5000;

    /// <summary>
    /// Gets or sets the number of shards for the Elasticsearch index.
    /// </summary>
    public int NumberOfShards { get; set; } = 1;

    /// <summary>
    /// Gets or sets the number of replicas for the Elasticsearch index.
    /// </summary>
    public int NumberOfReplicas { get; set; } = 1;

    /// <summary>
    /// Gets or sets whether to automatically register the index template.
    /// </summary>
    public bool AutoRegisterTemplate { get; set; } = true;

    /// <summary>
    /// Gets or sets the batch size for bulk operations.
    /// </summary>
    public int BatchPostingLimit { get; set; } = 50;

    /// <summary>
    /// Gets or sets the period between batch posts.
    /// </summary>
    public TimeSpan Period { get; set; } = TimeSpan.FromSeconds(2);

    /// <summary>
    /// Gets or sets whether to include fields in the log output.
    /// </summary>
    public bool IncludeFields { get; set; } = true;

    /// <summary>
    /// Gets or sets the minimum log level for Elasticsearch sink.
    /// </summary>
    public LogEventLevel MinimumLevel { get; set; } = LogEventLevel.Information;

    /// <summary>
    /// Gets or sets additional properties to include in log events.
    /// </summary>
    public Dictionary<string, object> AdditionalProperties { get; set; } = new();

    /// <summary>
    /// Gets the complete Logstash endpoint URI based on type, address, and port.
    /// </summary>
    public string LogstashEndpoint => LogstashType switch
    {
        LogstashTransportType.Tcp => $"tcp://{LogstashAddress}:{LogstashPort}",
        LogstashTransportType.Udp => $"udp://{LogstashAddress}:{LogstashPort}",
        _ => $"tcp://{LogstashAddress}:{LogstashPort}"
    };

    /// <summary>
    /// Gets whether the Logstash type is valid.
    /// </summary>
    public bool IsValidLogstashType => LogstashType is LogstashTransportType.Tcp or LogstashTransportType.Udp;
}

/// <summary>
/// Validates Elasticsearch configuration options.
/// </summary>
public sealed class ElasticsearchOptionsValidator : IValidateOptions<ElasticsearchOptions>
{
    public ValidateOptionsResult Validate(string? name, ElasticsearchOptions options)
    {
        var failures = new List<string>();

        // Validate URI
        if (options.ElasticsearchEnabled)
        {
            if (string.IsNullOrWhiteSpace(options.Uri))
            {
                failures.Add("Elasticsearch URI is required when Elasticsearch is enabled");
            }
            else if (!Uri.TryCreate(options.Uri, UriKind.Absolute, out var uri) ||
                     (uri.Scheme != "http" && uri.Scheme != "https"))
            {
                failures.Add("Elasticsearch URI must be a valid HTTP or HTTPS URL");
            }

            if (string.IsNullOrWhiteSpace(options.IndexName))
            {
                failures.Add("Elasticsearch IndexName is required when Elasticsearch is enabled");
            }
            else if (options.IndexName.Contains(' ') || !string.Equals(options.IndexName, options.IndexName.ToLowerInvariant(), StringComparison.Ordinal))
            {
                failures.Add("Elasticsearch IndexName must be lowercase and contain no spaces");
            }
        }

        // Validate Logstash configuration
        if (options.LogstashEnabled)
        {
            if (string.IsNullOrWhiteSpace(options.LogstashAddress))
            {
                failures.Add("Logstash address is required when Logstash is enabled");
            }

            if (options.LogstashPort <= 0 || options.LogstashPort > 65535)
            {
                failures.Add("Logstash port must be between 1 and 65535");
            }

            if (!options.IsValidLogstashType)
            {
                failures.Add("Logstash type must be either 'TCP' or 'UDP'");
            }
        }

        // Validate timeouts and limits
        if (options.ConnectionTimeout <= 0)
        {
            failures.Add("ConnectionTimeout must be greater than 0");
        }

        if (options.BatchPostingLimit <= 0)
        {
            failures.Add("BatchPostingLimit must be greater than 0");
        }

        if (options.Period <= TimeSpan.Zero)
        {
            failures.Add("Period must be greater than zero");
        }

        if (options.NumberOfShards < 1)
        {
            failures.Add("NumberOfShards must be at least 1");
        }

        if (options.NumberOfReplicas < 0)
        {
            failures.Add("NumberOfReplicas cannot be negative");
        }

        // Validate authentication - if username is provided, password should also be provided
        if (!string.IsNullOrWhiteSpace(options.Username) && string.IsNullOrWhiteSpace(options.Password))
        {
            failures.Add("Password is required when Username is provided");
        }

        return failures.Count > 0
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }
}
