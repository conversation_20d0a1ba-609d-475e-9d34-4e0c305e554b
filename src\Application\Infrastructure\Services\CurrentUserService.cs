using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Services;

public class CurrentUserService(
    IHttpContextAccessor httpContextAccessor,
    IOptions<UserOptions> options)
    : ICurrentUserService
{
    private readonly UserOptions _options = options.Value;

    public int? UserId
    {
        get
        {
            var userIdClaim = httpContextAccessor.HttpContext?.User.FindFirst("sub")?.Value;

            if (int.TryParse(userIdClaim, out var userId))
                return userId;

            return null;
        }
    }

    public bool IsWageUser(int userId) => userId == _options.WageUserId;

    public int WageUserId => _options.WageUserId;

    public bool IsIbanInquiryActive => _options.IsIbanInquiryActive;

    public bool IsBankValidationActive => _options.IsBankValidationActive;

    public long GetMinSettlementAmount => _options.MinSettlementAmount;

    public long GetMaxSettlementAmount => _options.MaxSettlementAmount;

    public decimal GetMaxLast24Amount => _options.MaxLast24Amount;

    public decimal DailyTransferDefaultLimit => _options.DailyTransferDefaultLimit;

    public bool IsFinnotechServiceActive => _options.IsFinnotechServiceActive;

    public int GetMaxSettlementCountPerRequest => _options.MaxSettlementCountPerRequest;

    public int IdenticalRequestLimitationHours => _options.IdenticalRequestLimitationHours;
}