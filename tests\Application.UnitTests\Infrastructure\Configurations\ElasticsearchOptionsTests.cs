using Serilog.Events;
using Shouldly;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.UnitTests.Infrastructure.Configurations;

public class ElasticsearchOptionsTests
{
    [Fact]
    public void LogstashEndpoint_WithTcpType_ShouldReturnTcpEndpoint()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            LogstashType = LogstashTransportType.Tcp,
            LogstashAddress = "localhost",
            LogstashPort = 9513
        };

        // Act
        var result = options.LogstashEndpoint;

        // Assert
        result.ShouldBe("tcp://localhost:9513");
    }

    [Fact]
    public void LogstashEndpoint_WithUdpType_ShouldReturnUdpEndpoint()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            LogstashType = LogstashTransportType.Udp,
            LogstashAddress = "localhost",
            LogstashPort = 9513
        };

        // Act
        var result = options.LogstashEndpoint;

        // Assert
        result.ShouldBe("udp://localhost:9513");
    }

    [Fact]
    public void LogstashEndpoint_WithInvalidType_ShouldReturnTcpEndpointAsDefault()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            LogstashType = LogstashTransportType.Unknown,
            LogstashAddress = "localhost",
            LogstashPort = 9513
        };

        // Act
        var result = options.LogstashEndpoint;

        // Assert
        result.ShouldBe("tcp://localhost:9513");
    }

    [Fact]
    public void LogstashEndpoint_WithLowercaseType_ShouldReturnCorrectEndpoint()
    {
        // Arrange
        var options = new ElasticsearchOptions
        {
            LogstashType = LogstashTransportType.Tcp,
            LogstashAddress = "localhost",
            LogstashPort = 9513
        };

        // Act
        var result = options.LogstashEndpoint;

        // Assert
        result.ShouldBe("tcp://localhost:9513");
    }

    [Fact]
    public void IsValidLogstashType_WithTcpType_ShouldReturnTrue()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = LogstashTransportType.Tcp };

        // Act
        var result = options.IsValidLogstashType;

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void IsValidLogstashType_WithUdpType_ShouldReturnTrue()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = LogstashTransportType.Udp };

        // Act
        var result = options.IsValidLogstashType;

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void IsValidLogstashType_WithLowercaseTypes_ShouldReturnTrue()
    {
        // Arrange
        var tcpOptions = new ElasticsearchOptions { LogstashType = LogstashTransportType.Tcp };
        var udpOptions = new ElasticsearchOptions { LogstashType = LogstashTransportType.Udp };

        // Act & Assert
        tcpOptions.IsValidLogstashType.ShouldBeTrue();
        udpOptions.IsValidLogstashType.ShouldBeTrue();
    }

    [Fact]
    public void IsValidLogstashType_WithInvalidType_ShouldReturnFalse()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = LogstashTransportType.Unknown };

        // Act
        var result = options.IsValidLogstashType;

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void DefaultValues_ShouldBeSetCorrectly()
    {
        // Arrange & Act
        var options = new ElasticsearchOptions();

        // Assert
        options.Uri.ShouldBe("http://localhost:9200");
        options.IndexName.ShouldBe("zify-settlement-logs");
        options.LogstashAddress.ShouldBe("localhost");
        options.LogstashPort.ShouldBe(9513);
        options.LogstashType.ShouldBe(LogstashTransportType.Tcp);
        options.ElasticsearchEnabled.ShouldBeFalse();
        options.LogstashEnabled.ShouldBeTrue();
        options.ConnectionTimeout.ShouldBe(5000);
        options.NumberOfShards.ShouldBe(1);
        options.NumberOfReplicas.ShouldBe(1);
        options.AutoRegisterTemplate.ShouldBeTrue();
        options.BatchPostingLimit.ShouldBe(50);
        options.Period.ShouldBe(TimeSpan.FromSeconds(2));
        options.IncludeFields.ShouldBeTrue();
        options.MinimumLevel.ShouldBe(LogEventLevel.Information);
        options.AdditionalProperties.ShouldNotBeNull();
        options.AdditionalProperties.Count.ShouldBe(0);
    }

    [Fact]
    public void SectionName_ShouldBeElasticsearch()
    {
        // Act & Assert
        ElasticsearchOptions.SectionName.ShouldBe("Elasticsearch");
    }
}
