using Shouldly;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.UnitTests.Domain;

public class UserConfigTests
{
    private UserConfig CreateUserConfigWithWalletInfo(int userId = 123, string? iban = null)
    {
        var walletInfo = UserWalletInformation.Create(userId, Guid.NewGuid());
        var userConfig = UserConfig.Create(walletInfo, 800_000_000);
        if (iban != null)
        {
            userConfig.UpdateIban(Iban.Of(iban), DateTime.UtcNow);
        }
        return userConfig;
    }

    #region CanUpdateIban Tests

    [Fact]
    public void CanUpdateIban_ShouldReturnTrue_WhenNoChangeHistoryExists()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void CanUpdateIban_ShouldReturnTrue_WhenUnderRateLimit()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Add one change within the time window (default limit is 1 per 24 hours)
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12));

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeFalse(); // Should be false because default limit is 1 per 24 hours
    }

    [Fact]
    public void CanUpdateIban_ShouldReturnFalse_WhenRateLimitExceeded()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Add changes that exceed the default limit (1 per 24 hours)
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-6));

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void CanUpdateIban_ShouldReturnTrue_WhenOldChangesAreOutsideTimeWindow()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Add changes outside the 24-hour window
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-25));
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-30));

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void CanUpdateIban_ShouldReturnTrue_WhenExactlyAtTimeWindowBoundary()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Add a change exactly at the 24-hour boundary (should be outside the window)
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-24));

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void CanUpdateIban_ShouldReturnFalse_WhenJustInsideTimeWindow()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Add a change just inside the 24-hour window
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-23).AddMinutes(-59));

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeFalse();
    }

    #endregion

    #region UpdateIban Tests

    [Fact]
    public void UpdateIban_ShouldUpdateIbanAndAddToHistory_WhenAllowed()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var newIban = Iban.Of("**************************");
        var updateTime = DateTime.UtcNow;

        // Act
        userConfig.UpdateIban(newIban, updateTime);

        // Assert
        userConfig.Iban.ShouldBe(newIban);
        userConfig.IbanChangeHistory.ShouldContain(updateTime);
        userConfig.IbanChangeHistory.Count.ShouldBe(1);
    }

    [Fact]
    public void UpdateIban_ShouldThrowException_WhenRateLimitExceeded()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var newIban = Iban.Of("**************************");
        var updateTime = DateTime.UtcNow;

        // Add a change within the time window to exceed the limit
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-12));

        // Act & Assert
        Should.Throw<IbanUpdateRateLimitExceededException>(() =>
            userConfig.UpdateIban(newIban, updateTime));
    }

    [Fact]
    public void UpdateIban_ShouldNotUpdate_WhenIbanIsSame()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var originalIban = userConfig.Iban;
        var updateTime = DateTime.UtcNow;

        // Act
        userConfig.UpdateIban(originalIban, updateTime);

        // Assert
        userConfig.Iban.ShouldBe(originalIban);
        userConfig.IbanChangeHistory.Count.ShouldBe(0); // Should not add to history if no change
    }

    [Fact]
    public void UpdateIban_ShouldCleanUpOldEntries_WhenUpdating()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var newIban = Iban.Of("**************************");
        var updateTime = DateTime.UtcNow;

        // Add old entries that should be cleaned up
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-25));
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-30));
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-48));

        // Act
        userConfig.UpdateIban(newIban, updateTime);

        // Assert
        userConfig.IbanChangeHistory.Count.ShouldBe(1); // Only the new entry should remain
        userConfig.IbanChangeHistory.ShouldContain(updateTime);
    }

    [Fact]
    public void UpdateIban_ShouldKeepRecentEntries_WhenCleaningUp()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var newIban = Iban.Of("**************************");
        var updateTime = DateTime.UtcNow;

        // Add a mix of old and recent entries
        var recentEntry = updateTime.AddHours(-12);
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-25)); // Should be removed
        userConfig.IbanChangeHistory.Add(recentEntry); // Should be kept
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-48)); // Should be removed

        // This should fail because we already have one recent entry
        // Act & Assert
        Should.Throw<IbanUpdateRateLimitExceededException>(() =>
            userConfig.UpdateIban(newIban, updateTime));
    }

    #endregion

    #region Configuration-Based Tests

    [Fact]
    public void CanUpdateIban_ShouldRespectConfigurationValues_WhenCheckingRateLimit()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // This test verifies that the method uses UserOptions configuration
        // With default values (24 hours, 1 max change), adding one change should block further updates
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12));

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void UpdateIban_ShouldUseConfigurationForCleanup_WhenRemovingOldEntries()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var newIban = Iban.Of("**************************");
        var updateTime = DateTime.UtcNow;

        // Add entries at various time points
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-23)); // Within window
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-24)); // At boundary
        userConfig.IbanChangeHistory.Add(updateTime.AddHours(-25)); // Outside window

        // Act & Assert
        // This should fail because there's already one entry within the 24-hour window
        Should.Throw<IbanUpdateRateLimitExceededException>(() =>
            userConfig.UpdateIban(newIban, updateTime));
    }

    #endregion

    #region Edge Cases

    [Fact]
    public void CanUpdateIban_ShouldHandleEmptyHistory_Correctly()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Ensure history is empty
        userConfig.IbanChangeHistory.Clear();

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void UpdateIban_ShouldHandleIdenticalIban_WithoutAddingToHistory()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var originalIban = userConfig.Iban;
        var updateTime = DateTime.UtcNow;
        var originalHistoryCount = userConfig.IbanChangeHistory.Count;

        // Act
        userConfig.UpdateIban(originalIban, updateTime);

        // Assert
        userConfig.Iban.ShouldBe(originalIban);
        userConfig.IbanChangeHistory.Count.ShouldBe(originalHistoryCount);
    }

    [Fact]
    public void UpdateIban_ShouldHandleMultipleOldEntries_WhenCleaning()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var newIban = Iban.Of("**************************");
        var updateTime = DateTime.UtcNow;

        // Add multiple old entries
        for (int i = 25; i <= 100; i += 5)
        {
            userConfig.IbanChangeHistory.Add(updateTime.AddHours(-i));
        }

        var oldEntriesCount = userConfig.IbanChangeHistory.Count;

        // Act
        userConfig.UpdateIban(newIban, updateTime);

        // Assert
        userConfig.IbanChangeHistory.Count.ShouldBe(1); // Only the new entry
        userConfig.IbanChangeHistory.ShouldContain(updateTime);
        oldEntriesCount.ShouldBeGreaterThan(1); // Verify we had multiple entries to clean
    }

    [Fact]
    public void CanUpdateIban_ShouldHandlePreciseTimeBoundaries_Correctly()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Add entry exactly at the boundary (should be considered outside the window)
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-24).AddMilliseconds(-1));

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void CanUpdateIban_ShouldCountOnlyEntriesWithinWindow_WhenMixedEntries()
    {
        // Arrange
        var userConfig = CreateUserConfigWithWalletInfo();
        var currentTime = DateTime.UtcNow;

        // Add entries both inside and outside the window
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-25)); // Outside
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-12)); // Inside
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-30)); // Outside
        userConfig.IbanChangeHistory.Add(currentTime.AddHours(-6));  // Inside (would exceed limit)

        // Act
        var result = userConfig.CanUpdateIban(currentTime);

        // Assert
        result.ShouldBeFalse(); // Should be false because there are 2 entries within the window (exceeds limit of 1)
    }

    #endregion
}
