using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;

public class UserWalletInformationConfiguration : IEntityTypeConfiguration<UserWalletInformation>
{
    public void Configure(EntityTypeBuilder<UserWalletInformation> builder)
    {
        builder.ToTable("UserWalletInformations");

        builder.<PERSON>Key(wi => wi.Id);

        builder.Property(wi => wi.Id)
               .ValueGeneratedOnAdd();

        builder.Property(wi => wi.UserId)
               .IsRequired();

        builder.Property(o => o.SettlementWalletId)
            .HasColumnName("SettlementWalletId")
            .HasConversion(
                v => v.HasValue ? v.Value.Value : (Guid?)null,
                v => v.HasValue ? WalletId.Of(v.Value) : null);

        builder.ComplexProperty(
               wi => wi.PaymentWalletId, buildAction =>
               {
                   buildAction.Property(p => p.Value)
                             .HasColumnName("PaymentWalletId")
                             .IsRequired();
               });
    }
}