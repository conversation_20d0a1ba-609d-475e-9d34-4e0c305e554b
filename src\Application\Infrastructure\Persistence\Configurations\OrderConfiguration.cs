﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Infrastructure.Persistence.Configurations;
public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.ToTable("Orders");

        builder.HasKey(o => o.Id);

        builder.Property(o => o.Id)
               .ValueGeneratedNever(); // Assuming ID is a Guid and set manually

        builder.Property(o => o.Title)
               .IsRequired()
               .HasMaxLength(200);
        
        builder.Property(o => o.Description)
               .IsRequired()
               .HasMaxLength(500);

        builder.Property(o => o.Status)
               .IsRequired();

        builder.Property(o => o.ScheduledTime)
               .IsRequired(false);

        builder.HasMany(o => o.OrderDetails)
               .WithOne(o => o.Order)
               .HasForeignKey(od => od.OrderId)
               .OnDelete(DeleteBehavior.Cascade);

        builder.Property(o => o.WalletBlockCorrelationId)
            .HasColumnName("WalletBlockCorrelationId")
            .HasConversion(
                v => v.HasValue ? v.Value.Value : (Guid?)null,
                v => v.HasValue ? WalletId.Of(v.Value) : null);

        builder.Property(o => o.WalletWithdrawCorrelationId)
            .HasColumnName("WalletWithdrawCorrelationId")
            .HasConversion(
                v => v.HasValue ? v.Value.Value : (Guid?)null,
                v => v.HasValue ? WalletId.Of(v.Value) : null);
    }
}
